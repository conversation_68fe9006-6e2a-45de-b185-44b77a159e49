import { format } from 'date-fns';
import { th } from 'date-fns/locale';

export type ComputedPropertiesOfTierReview = {
  scoreBias?: number;
  scoreCreative?: number;
  scoreFun?: number;
  scorePotential?: number;
  scoreGraphic?: number;
  scoreUi?: number;
  scoreAudio?: number;
  scoreControl?: number;
  scoreStability?: number;
  scoreBuy?: number;
  publishDate?: Date;
  publishDateFormatted?: string;
  finalScore?: number;
};

export function applyComputedTierReview<T extends object>(input: T) {
  const applied: Omit<T, 'publishDate'> & ComputedPropertiesOfTierReview = input as any;

  if ('publishDate' in applied) {
    if (typeof applied.publishDate === 'string') {
      applied.publishDate = new Date(applied.publishDate);
    }
  }

  applied.publishDateFormatted = (() => {
    return applied.publishDate ? format(applied.publishDate, 'dd MMMM yyyy', { locale: th }) : '';
  })();

  if (
    typeof applied.scoreBias === 'number' &&
    typeof applied.scoreCreative === 'number' &&
    typeof applied.scoreFun === 'number' &&
    typeof applied.scorePotential === 'number' &&
    typeof applied.scoreGraphic === 'number' &&
    typeof applied.scoreUi === 'number' &&
    typeof applied.scoreAudio === 'number' &&
    typeof applied.scoreControl === 'number' &&
    typeof applied.scoreStability === 'number' &&
    typeof applied.scoreBuy === 'number'
  ) {
    applied.finalScore = (() => {
      return (
        /* 10 */ applied.scoreBias * 0.1 +
        /* 25 */ applied.scoreCreative * 0.15 +
        /* 40 */ applied.scoreFun * 0.15 +
        /* 55 */ applied.scorePotential * 0.15 +
        /* 60 */ applied.scoreGraphic * 0.05 +
        /* 65 */ applied.scoreUi * 0.05 +
        /* 70 */ applied.scoreAudio * 0.05 +
        /* 80 */ applied.scoreControl * 0.1 +
        /* 90 */ applied.scoreStability * 0.1 +
        /* 00 */ applied.scoreBuy * 0.1
      );
    })();
  }

  return applied;
}
