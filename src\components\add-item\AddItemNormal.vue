<script setup lang="ts">
import { format } from 'date-fns';

import Compressor from '@uppy/compressor';
import Uppy from '@uppy/core';
import TUS from '@uppy/tus';
import Dashboard from '@uppy/vue/dashboard';
import { useVModels } from '@vueuse/core';

import type { TierLaneForMainPage } from '@/database/tierSet';

import type { NewItemData } from './TheAddItem.vue';

const props = defineProps({
  newItemList: {
    type: Array as () => NewItemData[],
    required: true,
  },
  lane: {
    type: Object as () => TierLaneForMainPage,
    required: true,
  },
  isLoading: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(['update:newItemList']);
const { newItemList } = useVModels(props, emit);

const uppy = new Uppy({
  id: 'uppy1',
  autoProceed: false,
  debug: import.meta.env.DEV,
  restrictions: {
    allowedFileTypes: ['image/*'],
    maxNumberOfFiles: 100,
  },
})
  .use(TUS, {
    endpoint: `${import.meta.env.PUBLIC_SUPABASE_URL}/storage/v1/upload/resumable`,
    headers: {
      authorization: `Bearer ${import.meta.env.PUBLIC_SUPABASE_ANON_KEY}`,
      apikey: import.meta.env.PUBLIC_SUPABASE_ANON_KEY,
    },
    uploadDataDuringCreation: true,
    chunkSize: 6 * 1024 * 1024,
    allowedMetaFields: ['bucketName', 'objectName', 'contentType', 'cacheControl'],
  })
  .use(Compressor)
  .on('file-added', file => {
    const supabaseMetadata = {
      bucketName: 'images',
      objectName: `uploads/normal/${format(new Date(), 'yyyy-MM-dd-hh:mm:ss')}_${file.name}`,
      contentType: file.type,
    };

    file.meta = {
      ...file.meta,
      ...supabaseMetadata,
    };

    console.log('file added', file);
  });

uppy.on('complete', result => {
  console.log('Upload complete', result);
  result.successful.forEach(file => {
    const imageUrl = `${import.meta.env.PUBLIC_SUPABASE_URL}/storage/v1/object/public/images/${
      file.meta.objectName
    }`;
    newItemList.value.push({
      _id: ++runningId.value,
      type: 'NORMAL',
      imageUrl: imageUrl,
      reviewTitle: '',
      targetUrl: '',
      lexorank: props.lane.tierItems?.at(-1)?.lexorank ?? '',
    });
  });
});

const runningId = ref(1);
</script>

<template>
  <div class="flex-auto">
    <label for="targetUrl" class="mb-1 block font-bold">
      Images <span class="text-theme-primary">*</span>
      <div class="text-sm font-light">(more images = more items)</div>
    </label>
    <div class="relative">
      <!-- https://uppy.io/docs/dashboard/ -->
      <Dashboard
        :uppy="uppy"
        :props="{
          metaFields: [{ id: 'name', name: 'Name', placeholder: 'File name' }],
          theme: 'dark',
          inline: true,
          width: '100%',
          height: '30vh',
        }"
        class="w-full"
      />
    </div>
  </div>
</template>

<style scss="scss" scoped></style>

<style src="@uppy/core/css/style.min.css"></style>
<style src="@uppy/dashboard/css/style.min.css"></style>
<style src="@uppy/drag-drop/dist/style.min.css"></style>
<style src="@uppy/progress-bar/dist/style.min.css"></style>
