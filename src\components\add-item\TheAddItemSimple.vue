<script setup lang="ts">
import { format } from 'date-fns';

import Uppy from '@uppy/core';
import DropTarget from '@uppy/drop-target';
import TUS from '@uppy/tus';
import Dashboard from '@uppy/vue/dashboard';
import { useVModels } from '@vueuse/core';

import { compressImage, fileToBase64 } from '@/helpers/file-compressor';

export type FileMeta = {
  imgData?: string;
  imgUrl?: string;
  bucketName?: string;
  objectName?: string;
  contentType?: string;
};

const props = defineProps({
  visible: {
    type: Boolean,
    required: true,
  },
  isUploading: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits<{
  (e: 'items-submitted', items: FileMeta[]): void;
  (e: 'update:visible', val: boolean): void;
  (e: 'update:isUploading', val: boolean): void;
}>();

const { visible, isUploading } = useVModels(props, emit);

async function onItemAdded(items: FileMeta[]) {
  emit('items-submitted', items);

  uppy.cancelAll();
  uppy.clear();

  visible.value = false;
}

const shouldUpload = ref(true);

const uppy = new Uppy<FileMeta>({
  id: 'uppy1',
  autoProceed: true,
  allowMultipleUploadBatches: false,
  debug: import.meta.env.DEV,
  restrictions: {
    allowedFileTypes: ['image/*'],
    maxNumberOfFiles: 100,
  },
});

/* BUG: uppy.use(Compressor) not working on calling uppy.addFiles */
// uppy.use(Compressor, { maxWidth: 300, maxHeight: 300, convertSize: 500_000 }); // 500KB
// uppy.use(ThumbnailGenerator, { thumbnailWidth: 300, thumbnailHeight: 300 });

uppy.addPreProcessor(async fileIds => {
  isUploading.value = true;
  for (const fileId of fileIds) {
    const file = uppy.getFile(fileId);
    const compressed = await compressImage(file.data);

    if (compressed) {
      uppy.setFileState(fileId, compressed);
      // uppy.setFileMeta(fileId, { });
    }
  }
});

uppy.use(DropTarget, {
  target: document.getElementById('main'),
});

// To Server
if (shouldUpload.value) {
  const BASE_URL = import.meta.env.PUBLIC_SUPABASE_URL;

  uppy.use(TUS, {
    endpoint: `${BASE_URL}/storage/v1/upload/resumable`,
    headers: {
      authorization: `Bearer ${import.meta.env.PUBLIC_SUPABASE_ANON_KEY}`,
    },
    uploadDataDuringCreation: true,
    chunkSize: 6 * 1024 * 1024,
    allowedMetaFields: ['bucketName', 'objectName', 'contentType', 'cacheControl'],
  });

  uppy.on('file-added', async file => {
    const objectName = `uploads/normal/${format(new Date(), 'yyyy-MM-dd-hh:mm:ss')}_${file.name}`;

    const supabaseMetadata = {
      bucketName: 'images',
      objectName,
      contentType: file.type,
    };

    Object.assign(file.meta, supabaseMetadata);

    console.log('File added to server', file);

    isUploading.value = false;
  });

  uppy.on('complete', result => {
    console.log('Upload completed to server', result);
    result.successful?.forEach(async file => {
      onItemAdded([
        {
          imgUrl: `${BASE_URL}/storage/v1/object/public/images/${file.meta.objectName}`,
        },
      ]);
    });
  });
}

// Locally
else {
  uppy.on('complete', result => {
    console.log('Upload completed locally', result);
    result.successful?.forEach(async file => {
      const imgData = await fileToBase64(file.data);
      if (imgData) {
        onItemAdded([{ imgData }]);
      }
    });
  });
}

onMounted(() => {
  document.body.addEventListener('paste', evt => {
    var items = evt.clipboardData!.items;
    for (const item of items) {
      if (item.kind === 'file') {
        var blob = item.getAsFile();
        var reader = new FileReader();
        reader.readAsDataURL(blob!);
        uppy.addFile(blob!);
      }
    }
  });
});
</script>

<template>
  <PrimeDialog v-model:visible="visible" modal header="New Items">
    <div class="flex-auto">
      <!-- https://uppy.io/docs/dashboard/ -->
      <Dashboard
        :uppy="uppy"
        :props="{
          metaFields: [{ id: 'name', name: 'Name', placeholder: 'File name' }],
          theme: 'dark',
          width: '100%',
          height: '30vh',
        }"
        class="w-full"
      />
    </div>
  </PrimeDialog>
</template>

<style scss="scss" scoped></style>

<style src="@uppy/core/css/style.min.css"></style>
<style src="@uppy/dashboard/css/style.min.css"></style>
<style src="@uppy/drag-drop/dist/style.min.css"></style>
<style src="@uppy/progress-bar/dist/style.min.css"></style>
<style src="@uppy/drop-target/css/style.min.css"></style>
