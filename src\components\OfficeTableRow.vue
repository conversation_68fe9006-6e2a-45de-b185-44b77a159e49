<script setup lang="ts">
import PrimeButton from 'primevue/button';
import Chip from 'primevue/chip';
import Galleria from 'primevue/galleria';
import ToggleSwitch from 'primevue/toggleswitch';
import { useToast } from 'primevue/usetoast';

import { Icon } from '@iconify/vue';
import { useThrottleFn, useVModels } from '@vueuse/core';

import ReviewWriter from '@/components/ReviewWriter.vue';
import TierItemLinks from '@/components/TierItemLinks.vue';
import { cn } from '@/helpers/cn';
import { trpc } from '@/services/trpc';
import { type GameSteamWithRating, pinia, useItemStore } from '@/stores/item';
import { useUserStore } from '@/stores/user';

const props = defineProps<{
  game: GameSteamWithRating;
  tierLanes: Awaited<ReturnType<typeof trpc.tierLane.retrieveMany.query>>;
}>();

const emit = defineEmits<{
  (e: 'updated:game', val: GameSteamWithRating): void;
  (e: 'rated', val: { gameSteamId: number; tierLaneSlug: string }): void;
}>();

const { game } = useVModels(props, emit);

const userStore = useUserStore(pinia);
const { isDemomanSpace } = storeToRefs(userStore);

const itemStore = useItemStore(pinia);
const { focusedGame } = storeToRefs(itemStore);

// State for managing score editing
const editingScore = reactive<Record<string, { isEditing: boolean; value: number | string }>>({});

// Function to start editing a score
function startEditingScore(tag: (typeof props.tierLanes)[0]) {
  if (!tag || !tag.canEdit) return;
  editingScore[tag.slug] = { isEditing: true, value: tag.score };
  // Optionally focus the input element after starting edit
  // nextTick(() => {
  //   const inputEl = gameRef.value?.querySelector(`input[data-tag-slug="${tag.slug}"]`);
  //   inputEl?.focus();
  // });
}

// Function to confirm and save the edited score
async function confirmScoreEdit(tag: (typeof props.tierLanes)[0]) {
  if (!tag || !tag.canEdit || !editingScore[tag.slug]) return;

  const editedData = editingScore[tag.slug];
  const newScore = Number(editedData.value);

  if (isNaN(newScore)) {
    console.error('Invalid score value');
    cancelScoreEdit(tag); // Cancel if not a valid number
    return;
  }

  try {
    // Assuming you have a tRPC mutation like 'tierLane.updateScore'
    // Adjust the mutation name and parameters as needed
    const updatedLane = await trpc.tierLane.updateScore.mutate({
      slug: tag.slug,
      score: newScore,
    });

    // Update the local game data optimistically or with the response
    const laneIndex = props.tierLanes.findIndex(tl => tl.slug === tag.slug);
    if (laneIndex !== -1) {
      // This might require adjusting how props/state are managed if tierLanes isn't directly mutable
      // Or refetching data might be simpler
      // For now, let's assume we can update the tag object directly if it's part of a reactive structure
      // or update the specific game's tierItem representation if that holds the score
      // This part depends heavily on your data structure and how tierLane scores are linked to games/tierItems
      // A simple approach: update the tag object passed in, assuming it's reactive
      Object.assign(tag, updatedLane); // Update the tag object with the response
    }

    // Update the specific tierItem score if it exists in the game object
    const tierItemIndex = game.value.tierItems.findIndex(ti => ti.tierLaneSlug === tag.slug);
    if (tierItemIndex !== -1) {
      // If score is directly on tierItem, update it. Adjust if score is on tierLane referenced by tierItem.
      // game.value.tierItems[tierItemIndex].score = newScore; // Example if score is on tierItem
    }

    delete editingScore[tag.slug]; // Exit editing mode
  } catch (error) {
    console.error('Failed to update score:', error);
    // Optionally handle error display to the user
    cancelScoreEdit(tag); // Cancel on error
  }
}

// Function to cancel score editing
function cancelScoreEdit(tag: (typeof props.tierLanes)[0]) {
  if (!tag || !editingScore[tag.slug]) return;
  delete editingScore[tag.slug];
}

const gameRef = useTemplateRef('gameRef');
const opening = ref(false);
const FOCUS_THRESHOLD = 300;

const tags = computed(() =>
  game.value.tierItems
    .map(ti => props.tierLanes.find(tl => tl.slug === ti.tierLaneSlug)!)
    .filter(Boolean)
    .toSorted((a, b) => b.score - a.score),
);

const isFocused = computed(() => focusedGame.value?.steamId === game.value.steamId);

function toggleVideo(e: Event, item: GameSteamWithRating['galleriaItems'][0]) {
  const buttonEl = e.target as HTMLButtonElement;
  const parent = buttonEl.closest('.preview-wrapper') as HTMLDivElement;
  const video = parent.querySelector('video') as HTMLVideoElement;

  video.src = video.src === item.teaser ? item.video! : item.teaser!;
  if (isFocused.value) {
    video.play(); // keep playing after source changed if focused.
  }
}

async function selectTierLane(tierLaneSlug: string) {
  const newGame = game.value;
  const action = newGame.rating[tierLaneSlug] ? 'remove' : 'add';
  console.log('selectTierLane:', action, tierLaneSlug);
  if (action === 'add') {
    const [newItem] = await trpc.tierItem.createMany.mutate({
      items: [{ gameSteamId: newGame.steamId, tierLaneSlug: tierLaneSlug }],
    });
    newGame.rating[tierLaneSlug] = true;
    newGame.tierItems.push(newItem);

    // Emit event after skipped
    if (tierLaneSlug.startsWith('hypeness')) {
      emit('rated', { gameSteamId: newGame.steamId, tierLaneSlug: tierLaneSlug });
    }
  } else {
    await trpc.tierItem.delete.mutate({
      gameSteamId: newGame.steamId,
      tierLaneSlug: tierLaneSlug,
    });
    delete newGame.rating[tierLaneSlug];
    newGame.tierItems.splice(newGame.tierItems.findIndex(ti => ti.tierLaneSlug === tierLaneSlug));
  }
  game.value = newGame;
}

const checkIfCentermost = () => {
  const rect = gameRef.value!.getBoundingClientRect();
  const elementCenter = rect.top + rect.height / 2;
  const distanceToCenter = Math.abs(elementCenter - window.innerHeight / 2);

  if (distanceToCenter < FOCUS_THRESHOLD) {
    if (!isFocused.value) {
      focusedGame.value = props.game;
      const videos = [...gameRef.value!.getElementsByTagName('video')];
      videos?.[0]?.play();
    }
  } else if (isFocused.value) {
    // Late set null if no game stole the focus
    setTimeout(() => {
      if (focusedGame.value?.steamId === props.game.steamId) {
        focusedGame.value = null;
      }
    }, 100);
    const videos = [...gameRef.value!.getElementsByTagName('video')];
    videos.forEach(video => video.pause());
  }
};
const throttledCheckCentermost = useThrottleFn(checkIfCentermost, 100);

onMounted(() => {
  throttledCheckCentermost();
  window.addEventListener('scroll', throttledCheckCentermost);
});

onUnmounted(() => {
  window.removeEventListener('scroll', throttledCheckCentermost);
});

const toast = useToast();
const isRefetching = ref(false);

async function refetchGameData() {
  if (isRefetching.value) return;

  try {
    isRefetching.value = true;
    const updatedGame = await trpc.gameSteam.refetch.mutate({
      steamId: game.value.steamId,
    });

    // Update the game with new data
    game.value = {
      ...updatedGame,
      rating: game.value.rating,
      galleriaItems: game.value.galleriaItems,
    };

    toast.add({
      severity: 'success',
      summary: 'Game Updated',
      detail: `${game.value.gameName} data refreshed from Steam`,
      life: 3000,
    });
  } catch (error) {
    console.error('Failed to refetch game data:', error);
    toast.add({
      severity: 'error',
      summary: 'Update Failed',
      detail: 'Could not refresh game data from Steam',
      life: 3000,
    });
  } finally {
    isRefetching.value = false;
  }
}

defineExpose({
  selectTierLane,
});
</script>

<template>
  <div
    ref="gameRef"
    :class="
      cn(
        'group/game relative flex max-w-screen flex-col gap-4 rounded-md bg-gray-500/10 p-3 md:flex-row', // Modified layout to flex-row on md screens
        isFocused ? 'is-focused' : 'not-focused',
        'transition-all duration-200', // Added transition for smoother focus effect
      )
    "
    :style="{
      '--tw-ring-color': tierLanes.find(tl => game.rating[tl.slug] && tl.tierSetSlug === 'suckz')
        ?.mainColor,
      '--tw-ring-offset-color': tierLanes.find(
        tl => game.rating[tl.slug] && tl.tierSetSlug === 'hypeness',
      )?.mainColor,
    }"
  >
    <div
      :class="cn('preview-wrapper group/preview order-1 w-full rounded-md md:order-none md:w-8/12')"
    >
      <Galleria
        :value="game.galleriaItems"
        :circular="false"
        :numVisible="5"
        :pt="{
          root: { class: 'overflow-visible' },
          content: { class: 'rounded-md shadow-lg' }, // Shadow for preview content
          thumbnailcontent: { class: 'rounded-b-md p-0.5' }, // Reduced thumbnail padding
          thumbnailsviewport: { class: 'h-9' }, // Reduced thumbnail viewport height
          thumbnailitem: { class: 'h-fit' },
        }"
      >
        <template #item="{ item }: { item: GameSteamWithRating['galleriaItems'][0] }">
          <div
            :class="
              cn(
                'preview-content group/preview',
                'flex h-full w-full items-center justify-center',
                'overflow-hidden rounded-md', // Ensure content stays within rounded bounds
              )
            "
          >
            <div v-if="'teaser' in item" :class="cn('relative h-full w-full')">
              <video
                :src="item.teaser"
                :class="cn('h-full w-full rounded-md object-cover')"
                loop
                controls
                __disabled_onmouseover="this.play()"
                __disabled_onmouseout="this.pause()"
              >
                <track label="English" kind="captions" srclang="en" default />
              </video>
              <div :class="cn('absolute top-1 right-1')">
                <PrimeButton
                  class="!text-3xs !bg-transparent"
                  label="HD"
                  @click="(e: Event) => toggleVideo(e, item)"
                />
              </div>
            </div>
            <template v-else-if="item.img">
              <img
                :src="item.img"
                :alt="game.gameName"
                :class="cn('h-full w-full rounded-md object-cover shadow-lg')"
              />
            </template>
          </div>
        </template>
        <template #thumbnail="{ item }: { item: GameSteamWithRating['galleriaItems'][0] }">
          <img
            v-if="item.img"
            :src="item.img"
            :alt="game.gameName"
            class="rounded-md object-cover"
            style="height: 100%; width: 100%"
          />
          <div v-else class="text-3xs h-32 w-32 bg-orange-300/20 p-2 text-center">
            <span class="w-full">{{ item.name }}</span>
          </div>
        </template>
      </Galleria>
    </div>

    <div class="order-2 flex w-full flex-col gap-3 md:order-none md:w-4/12">
      <div :class="cn('info-wrapper group/info rounded-md bg-gray-600/5 p-3')">
        <div class="flex items-baseline justify-between">
          <h3 class="text-2xl font-bold text-wrap text-gray-100">
            {{ game.gameName }}
          </h3>
          <div class="flex items-center gap-2">
            <TierItemLinks :tierItem="{ gameSteam: game, targetUrl: game.gameUrl }" />
            <!-- Add refetch button -->
            <PrimeButton
              v-if="isDemomanSpace"
              v-tooltip.left="'Refresh game data from Steam'"
              :loading="isRefetching"
              :class="
                cn(
                  '!bg-gray-700/50 !p-1 !text-xs hover:!bg-gray-600',
                  isRefetching ? 'animate-spin' : '',
                )
              "
              :disabled="isRefetching"
              @click="refetchGameData"
            >
              <Icon icon="lucide:refresh-cw" />
            </PrimeButton>
          </div>
        </div>

        <div class="line-clamp-2 text-sm">
          <div>{{ game.tierReview?.reviewTitle }}</div>
          <div class="text-xs">{{ game.tierReview?.publishDateFormatted }}</div>
        </div>

        <div class="mt-2 mb-2 line-clamp-5 text-sm text-gray-300">
          {{ game.shortDescription }}
        </div>
        <div class="mb-2 flex flex-wrap gap-0.5">
          <Chip
            v-for="tag in tags"
            :key="tag?.slug"
            :label="tag.label || tag.slug"
            :pt="{
              root: {
                class: 'm-0 h-6 cursor-default px-2 py-0.5 text-xs',
                style: {
                  '--main': tag.mainColor || '#333',
                  '--text': tag.textColor || '#fff',
                  backgroundColor: 'var(--main)',
                  color: 'var(--text)',
                  border: '1px solid color-mix(in srgb, var(--main) 80%, black)',
                },
              },
            }"
            rounded
          >
            <span v-if="tag.icon">{{ tag.icon }}</span>
            <span :class="cn('font-semibold', tag.score > 0 ? 'text-xs' : 'text-1.5xs')">
              {{ tag.label || tag.slug }}
            </span>

            <!-- Editable Score Section -->
            <template v-if="editingScore[tag.slug]?.isEditing">
              <input
                v-model="editingScore[tag.slug].value"
                type="number"
                :class="
                  cn(
                    'ml-1 w-10 rounded bg-black/30 px-1 py-0.5 text-[--text] ring-1',
                    'ring-white/50 outline-none ring-inset focus:ring-2 focus:ring-blue-500',
                  )
                "
                @keydown.enter.prevent="confirmScoreEdit(tag)"
                @keydown.esc.prevent="cancelScoreEdit(tag)"
                @blur="cancelScoreEdit(tag)"
              />
            </template>
            <template v-else>
              <!-- Display score conditionally -->
              <span
                v-if="tag.score !== 0"
                :class="
                  cn(
                    'text-xxs ml-1 rounded-full bg-black/20 px-1.5 py-0.5',
                    tag.canEdit ? 'cursor-pointer' : '',
                  )
                "
                :style="{ color: 'var(--text)' }"
                v-tooltip.top="`Score: ${tag.score}`"
                @click="startEditingScore(tag)"
              >
                {{ tag.score }}
              </span>
              <!-- Placeholder for score 0 -->
              <span
                v-else
                :class="cn('text-3xs ml-1 opacity-70', tag.canEdit ? 'cursor-pointer' : '')"
                v-tooltip.top="'Score: 0'"
                @click="startEditingScore(tag)"
              >
                -
              </span>
            </template>
            <!-- End Editable Score Section -->
          </Chip>
        </div>
        <div class="mb-1 text-xs text-gray-400">
          <span class="font-semibold">Dev:</span> {{ game.developers.join(', ') }} •
          <span class="font-semibold">Pub:</span> {{ game.publishers.join(', ') }}
        </div>
        <div class="text-xs text-gray-400">
          <span class="font-semibold">Created:</span> {{ game.createdAtFormatted }} •
          <span class="font-semibold">Released:</span> {{ game.releaseDateFormatted }}
        </div>
      </div>

      <div
        :class="cn('tier-lanes-wrapper group/col flex flex-col gap-2 rounded-md bg-gray-600/5 p-3')"
      >
        <h4 class="text-md mb-1 font-semibold text-gray-200">Ratings</h4>

        <div
          v-if="game.reviewTotal"
          class="flex items-center gap-1 px-2 text-xl"
          v-tooltip.left="'Steam Rating'"
        >
          <Icon icon="mdi:steam" class="text-gray-300" :inline="true" />
          <div :class="cn(game.reviewPercent! >= 75 ? 'text-green-400' : 'text-yellow-300')">
            <span class="font-bold text-green-400">
              {{ Intl.NumberFormat().format(game.reviewPositive!) }}
              /
              {{ Intl.NumberFormat().format(game.reviewTotal!) }}
            </span>
            <span v-if="game.reviewPercent != null" class="text-base opacity-80">
              ({{ game.reviewPercent }}%)
            </span>
          </div>
        </div>

        <div
          v-if="game.followerCount || game.peakCount"
          class="flex items-center gap-1 px-2 text-xl"
        >
          <div
            v-if="game.followerCount"
            class="flex items-center gap-1"
            v-tooltip.left="'Followers'"
          >
            <Icon icon="mdi:rss" class="text-gray-300" :inline="true" />
            <div class="font-bold text-green-400">
              {{ Intl.NumberFormat().format(game.followerCount) }}
            </div>
          </div>
          <div
            v-if="game.peakCount"
            class="flex items-center gap-1"
            v-tooltip.left="'Peak Players'"
          >
            <Icon icon="mdi:chart-line-variant" class="text-gray-300" :inline="true" />
            <div class="font-bold text-green-400">
              {{ Intl.NumberFormat().format(game.peakCount) }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <div v-if="game.tierReview" class="!absolute right-2 bottom-2">
      <ToggleSwitch v-model="opening" v-if="game.tierItems.length" />
    </div>
  </div>

  <div v-if="opening && game.tierReview" class="mt-8 w-full">
    <ReviewWriter
      v-model:tierItem="game.tierReview"
      v-model:id="game.steamId"
      v-model:reviewTitle="game.tierReview.reviewTitle!"
      v-model:reviewContent="game.tierReview.reviewContent!"
      v-model:publishDate="game.tierReview.publishDate!"
    />
  </div>
</template>

<style scoped lang="css">
@reference '@/styles/global.css';

.is-focused {
  @apply !opacity-100 shadow-lg ring-2 ring-transparent ring-offset-2 ring-offset-white !grayscale-0; /* Stronger focus effect */
  transform: scale3d(1.03, 1.03, 1.03); /* Slightly larger scale on focus */
}
.not-focused {
  @apply opacity-60 grayscale-50; /* Milder not-focused style */
}

.preview-content video::-webkit-media-controls-start-playback-button {
  display: none !important;
  -webkit-appearance: none;
}

.preview-content video::-webkit-media-controls-play-button,
.preview-content video::-webkit-media-controls-pause-button,
.preview-content video::-webkit-media-controls-current-time-display,
.preview-content video::-webkit-media-controls-time-remaining-display,
.preview-content video::-webkit-media-controls-timeline,
.preview-content video::-webkit-media-controls-volume-slider,
.preview-content video::-webkit-media-controls-mute-button,
.preview-content video::-webkit-media-controls-fullscreen-button {
  color: white; /* Control icons and text color */
}
</style>
