import { type InferResult } from 'kysely';
import { jsonArrayFrom, jsonObjectFrom } from 'kysely/helpers/postgres';
import { z } from 'zod';

import { TRPCError } from '@trpc/server';

import { type KyselyInstance, kysely } from '@/database/kysely';
import { applyComputedTierItem } from '@/database/tierItem';
import { logQuery } from '@/helpers/utils';

import { createRouter, publicProcedure } from '../trpc';

export const filterSchema = z.object({
  ids: z.array(z.number()).default([]),
  skip: z.number().int().min(0).default(0),
  take: z.number().int().min(0).default(100),
});

export const getTierItemFullQuery = (
  input: Partial<z.infer<typeof filterSchema>>,
  tx: KyselyInstance,
  userId?: string,
) => {
  const cleanInput = filterSchema.parse(input);
  const { ids, skip } = cleanInput;
  const take = Math.min(+cleanInput.take, 1000);
  console.log('getTierItemFullQuery', cleanInput);

  let query = tx
    .selectFrom('tierItem as ti')
    .selectAll()
    .innerJoin('gameSteam as gs', 'gs.steamId', 'ti.gameSteamId')
    .select(eb => [
      jsonObjectFrom(
        eb
          .selectFrom('gs')
          .selectAll()
          .select(eb2 => [
            jsonArrayFrom(
              eb2
                .selectFrom('tierItem as sti')
                .whereRef('sti.gameSteamId', '=', 'gs.steamId')
                .selectAll(),
            ).as('tierItems'),
          ]),
      ).as('gameSteam'),
    ]);

  if (ids.length) {
    query = query.where('ti.id', 'in', ids);
  }

  if (skip) {
    query = query.offset(skip);
  }

  if (userId) {
    query = query
      .innerJoin('tierLane as tl', 'tl.slug', 'ti.tierLaneSlug')
      .innerJoin('tierSet as ts', 'ts.slug', 'tl.tierSetSlug')
      .where(eb =>
        eb.or([
          eb('ts.permissionType', '=', 'PUBLIC'),
          eb(
            eb.selectFrom('player as p').where('p.userId', '=', userId).select('p.id'),
            'in',
            eb
              .selectFrom('playersOnSpaces as pos')
              .whereRef('pos.spaceId', '=', 'ts.spaceId')
              .select('pos.spaceId'),
          ),
        ]),
      );
  }

  return query;
};

export type ComputedTierItem = ReturnType<
  typeof applyComputedTierItem<InferResult<ReturnType<typeof getTierItemFullQuery>>[0]>
>;

export const tierItemRouter = createRouter({
  // ---------------------------
  list: publicProcedure.input(filterSchema).query(async ({ input, ctx: { user } }) => {
    const { skip, take } = input;

    const [result, count] = await kysely.transaction().execute(async tx => {
      const fullQuery = getTierItemFullQuery(input, tx);
      console.log(JSON.stringify(fullQuery, null, 2));

      const [result, count] = await Promise.all([
        fullQuery.execute(),
        fullQuery
          .clearSelect()
          .select(eb => eb.fn.countAll().as('count'))
          .executeTakeFirst()
          .then(r => +(r?.count as any)),
      ]);

      return [result, count];
    });

    const tierItems = result.map(applyComputedTierItem);

    const currentPage = Math.floor(skip / take);
    const totalPage = Math.ceil(count / take);

    // console.log(
    //   `fetchTierItems (${tierItems.length}): ${currentPage}/${totalPage}`,
    //   tierItems.slice(0, 3).map(item => ({ fullTitle: item.fullTitle, url: item.targetUrl })),
    // );

    return {
      data: tierItems,
      start: skip,
      end: skip + take,
      total: count,
      size: take,
      currentPage: currentPage,
      lastPage: totalPage,
    };
  }),

  // ---------------------------
  retrieve: publicProcedure
    .input(
      z.object({
        id: z.number(),
      }),
    )
    .query(async ({ input: { id }, ctx: { user } }) => {
      const fullQuery = getTierItemFullQuery({ ids: [id] }, kysely, user?.id);
      const result = await fullQuery.executeTakeFirst();
      const tierItem = result ? applyComputedTierItem(result) : null;
      return tierItem;
    }),
  // ---------------------------
  retrieveMany: publicProcedure.input(filterSchema).query(async ({ input, ctx: { user } }) => {
    const fullQuery = getTierItemFullQuery(input, kysely, user?.id);
    const result = await fullQuery.execute();
    const tierItems = result.map(applyComputedTierItem);
    return tierItems;
  }),
  // ---------------------------
  createMany: publicProcedure
    .input(
      z.object({
        items: z.array(
          z.object({
            tierLaneSlug: z.string().optional(),
            gameSteamId: z.number().positive(),
            lexorank: z.string().optional(),
          }),
        ),
      }),
    )
    .mutation(async ({ input, ctx: { user } }) => {
      if (!user) {
        throw new TRPCError({ code: 'UNAUTHORIZED' });
      }

      const result = await kysely.transaction().execute(async tx =>
        input.items.map(async item => {
          // Upsert into tierReview
          if (item.tierLaneSlug?.startsWith('suckz')) {
            const { score } = await tx
              .selectFrom('tierLane as tl')
              .where('tl.slug', '=', item.tierLaneSlug)
              .select('tl.score')
              .executeTakeFirstOrThrow();
            const SCORE_MAP: Record<string, number> = {
              '2016': 80,
              '2008': 60,
              '2004': 40,
              '2002': 20,
              '2001': 0,
            };
            const scoreBias = SCORE_MAP[`${score}`];
            const queryTr = tx
              .insertInto('tierReview')
              .values({ gameSteamId: item.gameSteamId, scoreBias })
              .onConflict(oc =>
                oc.column('gameSteamId').doUpdateSet({
                  scoreBias: eb => eb.ref('excluded.scoreBias'),
                }),
              )
              .returningAll();
            logQuery(queryTr);
            await queryTr.executeTakeFirstOrThrow();
          }

          // Upsert into tierItem
          const values: any = {};
          if (item.tierLaneSlug !== undefined) values.tierLaneSlug = item.tierLaneSlug;
          if (item.gameSteamId !== undefined) values.gameSteamId = item.gameSteamId;
          if (item.lexorank !== undefined) values.lexorank = item.lexorank;

          const queryTi = tx.insertInto('tierItem').values(values).returningAll();
          logQuery(queryTi);
          const createResult = await queryTi.executeTakeFirstOrThrow();

          return createResult;
        }),
      );

      const tierItems = result.map(applyComputedTierItem);
      return tierItems;
    }),
  // ---------------------------
  delete: publicProcedure
    .input(
      z.object({
        gameSteamId: z.number().positive(),
        tierLaneSlug: z.string(),
      }),
    )
    .mutation(async ({ input: { gameSteamId, tierLaneSlug }, ctx: { user } }) => {
      if (!user) {
        throw new TRPCError({ code: 'UNAUTHORIZED' });
      }

      const query = kysely
        .deleteFrom('tierItem as ti')
        .where('ti.gameSteamId', '=', gameSteamId)
        .where('ti.tierLaneSlug', '=', tierLaneSlug)
        .returningAll();

      const result = await query.executeTakeFirst();

      return result ? applyComputedTierItem(result) : null;
    }),
});

export type TierItemRouter = typeof tierItemRouter;
